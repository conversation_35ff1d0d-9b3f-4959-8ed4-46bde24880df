{"version": 3, "sources": ["../../../../node_modules/.pnpm/@radix-ui+react-tooltip@1.2_6ea00711e5a2ec239052d15c215aa770/node_modules/@radix-ui/react-tooltip/src/tooltip.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { useId } from '@radix-ui/react-id';\nimport * as PopperPrimitive from '@radix-ui/react-popper';\nimport { createPopperScope } from '@radix-ui/react-popper';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { createSlottable } from '@radix-ui/react-slot';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport * as VisuallyHiddenPrimitive from '@radix-ui/react-visually-hidden';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype ScopedProps<P = {}> = P & { __scopeTooltip?: Scope };\nconst [createTooltipContext, createTooltipScope] = createContextScope('Tooltip', [\n  createPopperScope,\n]);\nconst usePopperScope = createPopperScope();\n\n/* -------------------------------------------------------------------------------------------------\n * TooltipProvider\n * -----------------------------------------------------------------------------------------------*/\n\nconst PROVIDER_NAME = 'TooltipProvider';\nconst DEFAULT_DELAY_DURATION = 700;\nconst TOOLTIP_OPEN = 'tooltip.open';\n\ntype TooltipProviderContextValue = {\n  isOpenDelayedRef: React.MutableRefObject<boolean>;\n  delayDuration: number;\n  onOpen(): void;\n  onClose(): void;\n  onPointerInTransitChange(inTransit: boolean): void;\n  isPointerInTransitRef: React.MutableRefObject<boolean>;\n  disableHoverableContent: boolean;\n};\n\nconst [TooltipProviderContextProvider, useTooltipProviderContext] =\n  createTooltipContext<TooltipProviderContextValue>(PROVIDER_NAME);\n\ninterface TooltipProviderProps {\n  children: React.ReactNode;\n  /**\n   * The duration from when the pointer enters the trigger until the tooltip gets opened.\n   * @defaultValue 700\n   */\n  delayDuration?: number;\n  /**\n   * How much time a user has to enter another trigger without incurring a delay again.\n   * @defaultValue 300\n   */\n  skipDelayDuration?: number;\n  /**\n   * When `true`, trying to hover the content will result in the tooltip closing as the pointer leaves the trigger.\n   * @defaultValue false\n   */\n  disableHoverableContent?: boolean;\n}\n\nconst TooltipProvider: React.FC<TooltipProviderProps> = (\n  props: ScopedProps<TooltipProviderProps>\n) => {\n  const {\n    __scopeTooltip,\n    delayDuration = DEFAULT_DELAY_DURATION,\n    skipDelayDuration = 300,\n    disableHoverableContent = false,\n    children,\n  } = props;\n  const isOpenDelayedRef = React.useRef(true);\n  const isPointerInTransitRef = React.useRef(false);\n  const skipDelayTimerRef = React.useRef(0);\n\n  React.useEffect(() => {\n    const skipDelayTimer = skipDelayTimerRef.current;\n    return () => window.clearTimeout(skipDelayTimer);\n  }, []);\n\n  return (\n    <TooltipProviderContextProvider\n      scope={__scopeTooltip}\n      isOpenDelayedRef={isOpenDelayedRef}\n      delayDuration={delayDuration}\n      onOpen={React.useCallback(() => {\n        window.clearTimeout(skipDelayTimerRef.current);\n        isOpenDelayedRef.current = false;\n      }, [])}\n      onClose={React.useCallback(() => {\n        window.clearTimeout(skipDelayTimerRef.current);\n        skipDelayTimerRef.current = window.setTimeout(\n          () => (isOpenDelayedRef.current = true),\n          skipDelayDuration\n        );\n      }, [skipDelayDuration])}\n      isPointerInTransitRef={isPointerInTransitRef}\n      onPointerInTransitChange={React.useCallback((inTransit: boolean) => {\n        isPointerInTransitRef.current = inTransit;\n      }, [])}\n      disableHoverableContent={disableHoverableContent}\n    >\n      {children}\n    </TooltipProviderContextProvider>\n  );\n};\n\nTooltipProvider.displayName = PROVIDER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * Tooltip\n * -----------------------------------------------------------------------------------------------*/\n\nconst TOOLTIP_NAME = 'Tooltip';\n\ntype TooltipContextValue = {\n  contentId: string;\n  open: boolean;\n  stateAttribute: 'closed' | 'delayed-open' | 'instant-open';\n  trigger: TooltipTriggerElement | null;\n  onTriggerChange(trigger: TooltipTriggerElement | null): void;\n  onTriggerEnter(): void;\n  onTriggerLeave(): void;\n  onOpen(): void;\n  onClose(): void;\n  disableHoverableContent: boolean;\n};\n\nconst [TooltipContextProvider, useTooltipContext] =\n  createTooltipContext<TooltipContextValue>(TOOLTIP_NAME);\n\ninterface TooltipProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?: (open: boolean) => void;\n  /**\n   * The duration from when the pointer enters the trigger until the tooltip gets opened. This will\n   * override the prop with the same name passed to Provider.\n   * @defaultValue 700\n   */\n  delayDuration?: number;\n  /**\n   * When `true`, trying to hover the content will result in the tooltip closing as the pointer leaves the trigger.\n   * @defaultValue false\n   */\n  disableHoverableContent?: boolean;\n}\n\nconst Tooltip: React.FC<TooltipProps> = (props: ScopedProps<TooltipProps>) => {\n  const {\n    __scopeTooltip,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    disableHoverableContent: disableHoverableContentProp,\n    delayDuration: delayDurationProp,\n  } = props;\n  const providerContext = useTooltipProviderContext(TOOLTIP_NAME, props.__scopeTooltip);\n  const popperScope = usePopperScope(__scopeTooltip);\n  const [trigger, setTrigger] = React.useState<HTMLButtonElement | null>(null);\n  const contentId = useId();\n  const openTimerRef = React.useRef(0);\n  const disableHoverableContent =\n    disableHoverableContentProp ?? providerContext.disableHoverableContent;\n  const delayDuration = delayDurationProp ?? providerContext.delayDuration;\n  const wasOpenDelayedRef = React.useRef(false);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: (open) => {\n      if (open) {\n        providerContext.onOpen();\n\n        // as `onChange` is called within a lifecycle method we\n        // avoid dispatching via `dispatchDiscreteCustomEvent`.\n        document.dispatchEvent(new CustomEvent(TOOLTIP_OPEN));\n      } else {\n        providerContext.onClose();\n      }\n      onOpenChange?.(open);\n    },\n    caller: TOOLTIP_NAME,\n  });\n  const stateAttribute = React.useMemo(() => {\n    return open ? (wasOpenDelayedRef.current ? 'delayed-open' : 'instant-open') : 'closed';\n  }, [open]);\n\n  const handleOpen = React.useCallback(() => {\n    window.clearTimeout(openTimerRef.current);\n    openTimerRef.current = 0;\n    wasOpenDelayedRef.current = false;\n    setOpen(true);\n  }, [setOpen]);\n\n  const handleClose = React.useCallback(() => {\n    window.clearTimeout(openTimerRef.current);\n    openTimerRef.current = 0;\n    setOpen(false);\n  }, [setOpen]);\n\n  const handleDelayedOpen = React.useCallback(() => {\n    window.clearTimeout(openTimerRef.current);\n    openTimerRef.current = window.setTimeout(() => {\n      wasOpenDelayedRef.current = true;\n      setOpen(true);\n      openTimerRef.current = 0;\n    }, delayDuration);\n  }, [delayDuration, setOpen]);\n\n  React.useEffect(() => {\n    return () => {\n      if (openTimerRef.current) {\n        window.clearTimeout(openTimerRef.current);\n        openTimerRef.current = 0;\n      }\n    };\n  }, []);\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <TooltipContextProvider\n        scope={__scopeTooltip}\n        contentId={contentId}\n        open={open}\n        stateAttribute={stateAttribute}\n        trigger={trigger}\n        onTriggerChange={setTrigger}\n        onTriggerEnter={React.useCallback(() => {\n          if (providerContext.isOpenDelayedRef.current) handleDelayedOpen();\n          else handleOpen();\n        }, [providerContext.isOpenDelayedRef, handleDelayedOpen, handleOpen])}\n        onTriggerLeave={React.useCallback(() => {\n          if (disableHoverableContent) {\n            handleClose();\n          } else {\n            // Clear the timer in case the pointer leaves the trigger before the tooltip is opened.\n            window.clearTimeout(openTimerRef.current);\n            openTimerRef.current = 0;\n          }\n        }, [handleClose, disableHoverableContent])}\n        onOpen={handleOpen}\n        onClose={handleClose}\n        disableHoverableContent={disableHoverableContent}\n      >\n        {children}\n      </TooltipContextProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nTooltip.displayName = TOOLTIP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TooltipTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'TooltipTrigger';\n\ntype TooltipTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface TooltipTriggerProps extends PrimitiveButtonProps {}\n\nconst TooltipTrigger = React.forwardRef<TooltipTriggerElement, TooltipTriggerProps>(\n  (props: ScopedProps<TooltipTriggerProps>, forwardedRef) => {\n    const { __scopeTooltip, ...triggerProps } = props;\n    const context = useTooltipContext(TRIGGER_NAME, __scopeTooltip);\n    const providerContext = useTooltipProviderContext(TRIGGER_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const ref = React.useRef<TooltipTriggerElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref, context.onTriggerChange);\n    const isPointerDownRef = React.useRef(false);\n    const hasPointerMoveOpenedRef = React.useRef(false);\n    const handlePointerUp = React.useCallback(() => (isPointerDownRef.current = false), []);\n\n    React.useEffect(() => {\n      return () => document.removeEventListener('pointerup', handlePointerUp);\n    }, [handlePointerUp]);\n\n    return (\n      <PopperPrimitive.Anchor asChild {...popperScope}>\n        <Primitive.button\n          // We purposefully avoid adding `type=button` here because tooltip triggers are also\n          // commonly anchors and the anchor `type` attribute signifies MIME type.\n          aria-describedby={context.open ? context.contentId : undefined}\n          data-state={context.stateAttribute}\n          {...triggerProps}\n          ref={composedRefs}\n          onPointerMove={composeEventHandlers(props.onPointerMove, (event) => {\n            if (event.pointerType === 'touch') return;\n            if (\n              !hasPointerMoveOpenedRef.current &&\n              !providerContext.isPointerInTransitRef.current\n            ) {\n              context.onTriggerEnter();\n              hasPointerMoveOpenedRef.current = true;\n            }\n          })}\n          onPointerLeave={composeEventHandlers(props.onPointerLeave, () => {\n            context.onTriggerLeave();\n            hasPointerMoveOpenedRef.current = false;\n          })}\n          onPointerDown={composeEventHandlers(props.onPointerDown, () => {\n            if (context.open) {\n              context.onClose();\n            }\n            isPointerDownRef.current = true;\n            document.addEventListener('pointerup', handlePointerUp, { once: true });\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => {\n            if (!isPointerDownRef.current) context.onOpen();\n          })}\n          onBlur={composeEventHandlers(props.onBlur, context.onClose)}\n          onClick={composeEventHandlers(props.onClick, context.onClose)}\n        />\n      </PopperPrimitive.Anchor>\n    );\n  }\n);\n\nTooltipTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TooltipPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'TooltipPortal';\n\ntype PortalContextValue = { forceMount?: true };\nconst [PortalProvider, usePortalContext] = createTooltipContext<PortalContextValue>(PORTAL_NAME, {\n  forceMount: undefined,\n});\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface TooltipPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst TooltipPortal: React.FC<TooltipPortalProps> = (props: ScopedProps<TooltipPortalProps>) => {\n  const { __scopeTooltip, forceMount, children, container } = props;\n  const context = useTooltipContext(PORTAL_NAME, __scopeTooltip);\n  return (\n    <PortalProvider scope={__scopeTooltip} forceMount={forceMount}>\n      <Presence present={forceMount || context.open}>\n        <PortalPrimitive asChild container={container}>\n          {children}\n        </PortalPrimitive>\n      </Presence>\n    </PortalProvider>\n  );\n};\n\nTooltipPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TooltipContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'TooltipContent';\n\ntype TooltipContentElement = TooltipContentImplElement;\ninterface TooltipContentProps extends TooltipContentImplProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst TooltipContent = React.forwardRef<TooltipContentElement, TooltipContentProps>(\n  (props: ScopedProps<TooltipContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeTooltip);\n    const { forceMount = portalContext.forceMount, side = 'top', ...contentProps } = props;\n    const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n\n    return (\n      <Presence present={forceMount || context.open}>\n        {context.disableHoverableContent ? (\n          <TooltipContentImpl side={side} {...contentProps} ref={forwardedRef} />\n        ) : (\n          <TooltipContentHoverable side={side} {...contentProps} ref={forwardedRef} />\n        )}\n      </Presence>\n    );\n  }\n);\n\ntype Point = { x: number; y: number };\ntype Polygon = Point[];\n\ntype TooltipContentHoverableElement = TooltipContentImplElement;\ninterface TooltipContentHoverableProps extends TooltipContentImplProps {}\n\nconst TooltipContentHoverable = React.forwardRef<\n  TooltipContentHoverableElement,\n  TooltipContentHoverableProps\n>((props: ScopedProps<TooltipContentHoverableProps>, forwardedRef) => {\n  const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n  const providerContext = useTooltipProviderContext(CONTENT_NAME, props.__scopeTooltip);\n  const ref = React.useRef<TooltipContentHoverableElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const [pointerGraceArea, setPointerGraceArea] = React.useState<Polygon | null>(null);\n\n  const { trigger, onClose } = context;\n  const content = ref.current;\n\n  const { onPointerInTransitChange } = providerContext;\n\n  const handleRemoveGraceArea = React.useCallback(() => {\n    setPointerGraceArea(null);\n    onPointerInTransitChange(false);\n  }, [onPointerInTransitChange]);\n\n  const handleCreateGraceArea = React.useCallback(\n    (event: PointerEvent, hoverTarget: HTMLElement) => {\n      const currentTarget = event.currentTarget as HTMLElement;\n      const exitPoint = { x: event.clientX, y: event.clientY };\n      const exitSide = getExitSideFromRect(exitPoint, currentTarget.getBoundingClientRect());\n      const paddedExitPoints = getPaddedExitPoints(exitPoint, exitSide);\n      const hoverTargetPoints = getPointsFromRect(hoverTarget.getBoundingClientRect());\n      const graceArea = getHull([...paddedExitPoints, ...hoverTargetPoints]);\n      setPointerGraceArea(graceArea);\n      onPointerInTransitChange(true);\n    },\n    [onPointerInTransitChange]\n  );\n\n  React.useEffect(() => {\n    return () => handleRemoveGraceArea();\n  }, [handleRemoveGraceArea]);\n\n  React.useEffect(() => {\n    if (trigger && content) {\n      const handleTriggerLeave = (event: PointerEvent) => handleCreateGraceArea(event, content);\n      const handleContentLeave = (event: PointerEvent) => handleCreateGraceArea(event, trigger);\n\n      trigger.addEventListener('pointerleave', handleTriggerLeave);\n      content.addEventListener('pointerleave', handleContentLeave);\n      return () => {\n        trigger.removeEventListener('pointerleave', handleTriggerLeave);\n        content.removeEventListener('pointerleave', handleContentLeave);\n      };\n    }\n  }, [trigger, content, handleCreateGraceArea, handleRemoveGraceArea]);\n\n  React.useEffect(() => {\n    if (pointerGraceArea) {\n      const handleTrackPointerGrace = (event: PointerEvent) => {\n        const target = event.target as HTMLElement;\n        const pointerPosition = { x: event.clientX, y: event.clientY };\n        const hasEnteredTarget = trigger?.contains(target) || content?.contains(target);\n        const isPointerOutsideGraceArea = !isPointInPolygon(pointerPosition, pointerGraceArea);\n\n        if (hasEnteredTarget) {\n          handleRemoveGraceArea();\n        } else if (isPointerOutsideGraceArea) {\n          handleRemoveGraceArea();\n          onClose();\n        }\n      };\n      document.addEventListener('pointermove', handleTrackPointerGrace);\n      return () => document.removeEventListener('pointermove', handleTrackPointerGrace);\n    }\n  }, [trigger, content, pointerGraceArea, onClose, handleRemoveGraceArea]);\n\n  return <TooltipContentImpl {...props} ref={composedRefs} />;\n});\n\nconst [VisuallyHiddenContentContextProvider, useVisuallyHiddenContentContext] =\n  createTooltipContext(TOOLTIP_NAME, { isInside: false });\n\ntype TooltipContentImplElement = React.ComponentRef<typeof PopperPrimitive.Content>;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype PopperContentProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Content>;\ninterface TooltipContentImplProps extends Omit<PopperContentProps, 'onPlaced'> {\n  /**\n   * A more descriptive label for accessibility purpose\n   */\n  'aria-label'?: string;\n\n  /**\n   * Event handler called when the escape key is down.\n   * Can be prevented.\n   */\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  /**\n   * Event handler called when the a `pointerdown` event happens outside of the `Tooltip`.\n   * Can be prevented.\n   */\n  onPointerDownOutside?: DismissableLayerProps['onPointerDownOutside'];\n}\n\nconst Slottable = createSlottable('TooltipContent');\n\nconst TooltipContentImpl = React.forwardRef<TooltipContentImplElement, TooltipContentImplProps>(\n  (props: ScopedProps<TooltipContentImplProps>, forwardedRef) => {\n    const {\n      __scopeTooltip,\n      children,\n      'aria-label': ariaLabel,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      ...contentProps\n    } = props;\n    const context = useTooltipContext(CONTENT_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const { onClose } = context;\n\n    // Close this tooltip if another one opens\n    React.useEffect(() => {\n      document.addEventListener(TOOLTIP_OPEN, onClose);\n      return () => document.removeEventListener(TOOLTIP_OPEN, onClose);\n    }, [onClose]);\n\n    // Close the tooltip if the trigger is scrolled\n    React.useEffect(() => {\n      if (context.trigger) {\n        const handleScroll = (event: Event) => {\n          const target = event.target as HTMLElement;\n          if (target?.contains(context.trigger)) onClose();\n        };\n        window.addEventListener('scroll', handleScroll, { capture: true });\n        return () => window.removeEventListener('scroll', handleScroll, { capture: true });\n      }\n    }, [context.trigger, onClose]);\n\n    return (\n      <DismissableLayer\n        asChild\n        disableOutsidePointerEvents={false}\n        onEscapeKeyDown={onEscapeKeyDown}\n        onPointerDownOutside={onPointerDownOutside}\n        onFocusOutside={(event) => event.preventDefault()}\n        onDismiss={onClose}\n      >\n        <PopperPrimitive.Content\n          data-state={context.stateAttribute}\n          {...popperScope}\n          {...contentProps}\n          ref={forwardedRef}\n          style={{\n            ...contentProps.style,\n            // re-namespace exposed content custom properties\n            ...{\n              '--radix-tooltip-content-transform-origin': 'var(--radix-popper-transform-origin)',\n              '--radix-tooltip-content-available-width': 'var(--radix-popper-available-width)',\n              '--radix-tooltip-content-available-height': 'var(--radix-popper-available-height)',\n              '--radix-tooltip-trigger-width': 'var(--radix-popper-anchor-width)',\n              '--radix-tooltip-trigger-height': 'var(--radix-popper-anchor-height)',\n            },\n          }}\n        >\n          <Slottable>{children}</Slottable>\n          <VisuallyHiddenContentContextProvider scope={__scopeTooltip} isInside={true}>\n            <VisuallyHiddenPrimitive.Root id={context.contentId} role=\"tooltip\">\n              {ariaLabel || children}\n            </VisuallyHiddenPrimitive.Root>\n          </VisuallyHiddenContentContextProvider>\n        </PopperPrimitive.Content>\n      </DismissableLayer>\n    );\n  }\n);\n\nTooltipContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TooltipArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'TooltipArrow';\n\ntype TooltipArrowElement = React.ComponentRef<typeof PopperPrimitive.Arrow>;\ntype PopperArrowProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Arrow>;\ninterface TooltipArrowProps extends PopperArrowProps {}\n\nconst TooltipArrow = React.forwardRef<TooltipArrowElement, TooltipArrowProps>(\n  (props: ScopedProps<TooltipArrowProps>, forwardedRef) => {\n    const { __scopeTooltip, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeTooltip);\n    const visuallyHiddenContentContext = useVisuallyHiddenContentContext(\n      ARROW_NAME,\n      __scopeTooltip\n    );\n    // if the arrow is inside the `VisuallyHidden`, we don't want to render it all to\n    // prevent issues in positioning the arrow due to the duplicate\n    return visuallyHiddenContentContext.isInside ? null : (\n      <PopperPrimitive.Arrow {...popperScope} {...arrowProps} ref={forwardedRef} />\n    );\n  }\n);\n\nTooltipArrow.displayName = ARROW_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype Side = NonNullable<TooltipContentProps['side']>;\n\nfunction getExitSideFromRect(point: Point, rect: DOMRect): Side {\n  const top = Math.abs(rect.top - point.y);\n  const bottom = Math.abs(rect.bottom - point.y);\n  const right = Math.abs(rect.right - point.x);\n  const left = Math.abs(rect.left - point.x);\n\n  switch (Math.min(top, bottom, right, left)) {\n    case left:\n      return 'left';\n    case right:\n      return 'right';\n    case top:\n      return 'top';\n    case bottom:\n      return 'bottom';\n    default:\n      throw new Error('unreachable');\n  }\n}\n\nfunction getPaddedExitPoints(exitPoint: Point, exitSide: Side, padding = 5) {\n  const paddedExitPoints: Point[] = [];\n  switch (exitSide) {\n    case 'top':\n      paddedExitPoints.push(\n        { x: exitPoint.x - padding, y: exitPoint.y + padding },\n        { x: exitPoint.x + padding, y: exitPoint.y + padding }\n      );\n      break;\n    case 'bottom':\n      paddedExitPoints.push(\n        { x: exitPoint.x - padding, y: exitPoint.y - padding },\n        { x: exitPoint.x + padding, y: exitPoint.y - padding }\n      );\n      break;\n    case 'left':\n      paddedExitPoints.push(\n        { x: exitPoint.x + padding, y: exitPoint.y - padding },\n        { x: exitPoint.x + padding, y: exitPoint.y + padding }\n      );\n      break;\n    case 'right':\n      paddedExitPoints.push(\n        { x: exitPoint.x - padding, y: exitPoint.y - padding },\n        { x: exitPoint.x - padding, y: exitPoint.y + padding }\n      );\n      break;\n  }\n  return paddedExitPoints;\n}\n\nfunction getPointsFromRect(rect: DOMRect) {\n  const { top, right, bottom, left } = rect;\n  return [\n    { x: left, y: top },\n    { x: right, y: top },\n    { x: right, y: bottom },\n    { x: left, y: bottom },\n  ];\n}\n\n// Determine if a point is inside of a polygon.\n// Based on https://github.com/substack/point-in-polygon\nfunction isPointInPolygon(point: Point, polygon: Polygon) {\n  const { x, y } = point;\n  let inside = false;\n  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {\n    const ii = polygon[i]!;\n    const jj = polygon[j]!;\n    const xi = ii.x;\n    const yi = ii.y;\n    const xj = jj.x;\n    const yj = jj.y;\n\n    // prettier-ignore\n    const intersect = ((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi);\n    if (intersect) inside = !inside;\n  }\n\n  return inside;\n}\n\n// Returns a new array of points representing the convex hull of the given set of points.\n// https://www.nayuki.io/page/convex-hull-algorithm\nfunction getHull<P extends Point>(points: Readonly<Array<P>>): Array<P> {\n  const newPoints: Array<P> = points.slice();\n  newPoints.sort((a: Point, b: Point) => {\n    if (a.x < b.x) return -1;\n    else if (a.x > b.x) return +1;\n    else if (a.y < b.y) return -1;\n    else if (a.y > b.y) return +1;\n    else return 0;\n  });\n  return getHullPresorted(newPoints);\n}\n\n// Returns the convex hull, assuming that each points[i] <= points[i + 1]. Runs in O(n) time.\nfunction getHullPresorted<P extends Point>(points: Readonly<Array<P>>): Array<P> {\n  if (points.length <= 1) return points.slice();\n\n  const upperHull: Array<P> = [];\n  for (let i = 0; i < points.length; i++) {\n    const p = points[i]!;\n    while (upperHull.length >= 2) {\n      const q = upperHull[upperHull.length - 1]!;\n      const r = upperHull[upperHull.length - 2]!;\n      if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) upperHull.pop();\n      else break;\n    }\n    upperHull.push(p);\n  }\n  upperHull.pop();\n\n  const lowerHull: Array<P> = [];\n  for (let i = points.length - 1; i >= 0; i--) {\n    const p = points[i]!;\n    while (lowerHull.length >= 2) {\n      const q = lowerHull[lowerHull.length - 1]!;\n      const r = lowerHull[lowerHull.length - 2]!;\n      if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) lowerHull.pop();\n      else break;\n    }\n    lowerHull.push(p);\n  }\n  lowerHull.pop();\n\n  if (\n    upperHull.length === 1 &&\n    lowerHull.length === 1 &&\n    upperHull[0]!.x === lowerHull[0]!.x &&\n    upperHull[0]!.y === lowerHull[0]!.y\n  ) {\n    return upperHull;\n  } else {\n    return upperHull.concat(lowerHull);\n  }\n}\n\nconst Provider = TooltipProvider;\nconst Root = Tooltip;\nconst Trigger = TooltipTrigger;\nconst Portal = TooltipPortal;\nconst Content = TooltipContent;\nconst Arrow = TooltipArrow;\n\nexport {\n  createTooltipScope,\n  //\n  TooltipProvider,\n  Tooltip,\n  TooltipTrigger,\n  TooltipPortal,\n  TooltipContent,\n  TooltipArrow,\n  //\n  Provider,\n  Root,\n  Trigger,\n  Portal,\n  Content,\n  Arrow,\n};\nexport type {\n  TooltipProviderProps,\n  TooltipProps,\n  TooltipTriggerProps,\n  TooltipPortalProps,\n  TooltipContentProps,\n  TooltipArrowProps,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,YAAuB;AAmFnB,yBAAA;AAjEJ,IAAM,CAAC,sBAAsB,kBAAkB,IAAI,mBAAmB,WAAW;EAC/E;AACF,CAAC;AACD,IAAM,iBAAiB,kBAAkB;AAMzC,IAAM,gBAAgB;AACtB,IAAM,yBAAyB;AAC/B,IAAM,eAAe;AAYrB,IAAM,CAAC,gCAAgC,yBAAyB,IAC9D,qBAAkD,aAAa;AAqBjE,IAAM,kBAAkD,CACtD,UACG;AACH,QAAM;IACJ;IACA,gBAAgB;IAChB,oBAAoB;IACpB,0BAA0B;IAC1B;EACF,IAAI;AACJ,QAAM,mBAAyB,aAAO,IAAI;AAC1C,QAAM,wBAA8B,aAAO,KAAK;AAChD,QAAM,oBAA0B,aAAO,CAAC;AAElC,EAAA,gBAAU,MAAM;AACpB,UAAM,iBAAiB,kBAAkB;AACzC,WAAO,MAAM,OAAO,aAAa,cAAc;EACjD,GAAG,CAAC,CAAC;AAEL,aACE;IAAC;IAAA;MACC,OAAO;MACP;MACA;MACA,QAAc,kBAAY,MAAM;AAC9B,eAAO,aAAa,kBAAkB,OAAO;AAC7C,yBAAiB,UAAU;MAC7B,GAAG,CAAC,CAAC;MACL,SAAe,kBAAY,MAAM;AAC/B,eAAO,aAAa,kBAAkB,OAAO;AAC7C,0BAAkB,UAAU,OAAO;UACjC,MAAO,iBAAiB,UAAU;UAClC;QACF;MACF,GAAG,CAAC,iBAAiB,CAAC;MACtB;MACA,0BAAgC,kBAAY,CAAC,cAAuB;AAClE,8BAAsB,UAAU;MAClC,GAAG,CAAC,CAAC;MACL;MAEC;IAAA;EACH;AAEJ;AAEA,gBAAgB,cAAc;AAM9B,IAAM,eAAe;AAerB,IAAM,CAAC,wBAAwB,iBAAiB,IAC9C,qBAA0C,YAAY;AAoBxD,IAAM,UAAkC,CAAC,UAAqC;AAC5E,QAAM;IACJ;IACA;IACA,MAAM;IACN;IACA;IACA,yBAAyB;IACzB,eAAe;EACjB,IAAI;AACJ,QAAM,kBAAkB,0BAA0B,cAAc,MAAM,cAAc;AACpF,QAAM,cAAc,eAAe,cAAc;AACjD,QAAM,CAAC,SAAS,UAAU,IAAU,eAAmC,IAAI;AAC3E,QAAM,YAAY,MAAM;AACxB,QAAM,eAAqB,aAAO,CAAC;AACnC,QAAM,0BACJ,+BAA+B,gBAAgB;AACjD,QAAM,gBAAgB,qBAAqB,gBAAgB;AAC3D,QAAM,oBAA0B,aAAO,KAAK;AAC5C,QAAM,CAAC,MAAM,OAAO,IAAI,qBAAqB;IAC3C,MAAM;IACN,aAAa,eAAe;IAC5B,UAAU,CAACA,UAAS;AAClB,UAAIA,OAAM;AACR,wBAAgB,OAAO;AAIvB,iBAAS,cAAc,IAAI,YAAY,YAAY,CAAC;MACtD,OAAO;AACL,wBAAgB,QAAQ;MAC1B;AACA,mDAAeA;IACjB;IACA,QAAQ;EACV,CAAC;AACD,QAAM,iBAAuB,cAAQ,MAAM;AACzC,WAAO,OAAQ,kBAAkB,UAAU,iBAAiB,iBAAkB;EAChF,GAAG,CAAC,IAAI,CAAC;AAET,QAAM,aAAmB,kBAAY,MAAM;AACzC,WAAO,aAAa,aAAa,OAAO;AACxC,iBAAa,UAAU;AACvB,sBAAkB,UAAU;AAC5B,YAAQ,IAAI;EACd,GAAG,CAAC,OAAO,CAAC;AAEZ,QAAM,cAAoB,kBAAY,MAAM;AAC1C,WAAO,aAAa,aAAa,OAAO;AACxC,iBAAa,UAAU;AACvB,YAAQ,KAAK;EACf,GAAG,CAAC,OAAO,CAAC;AAEZ,QAAM,oBAA0B,kBAAY,MAAM;AAChD,WAAO,aAAa,aAAa,OAAO;AACxC,iBAAa,UAAU,OAAO,WAAW,MAAM;AAC7C,wBAAkB,UAAU;AAC5B,cAAQ,IAAI;AACZ,mBAAa,UAAU;IACzB,GAAG,aAAa;EAClB,GAAG,CAAC,eAAe,OAAO,CAAC;AAErB,EAAA,gBAAU,MAAM;AACpB,WAAO,MAAM;AACX,UAAI,aAAa,SAAS;AACxB,eAAO,aAAa,aAAa,OAAO;AACxC,qBAAa,UAAU;MACzB;IACF;EACF,GAAG,CAAC,CAAC;AAEL,aACE,wBAAiB,OAAhB,EAAsB,GAAG,aACxB,cAAA;IAAC;IAAA;MACC,OAAO;MACP;MACA;MACA;MACA;MACA,iBAAiB;MACjB,gBAAsB,kBAAY,MAAM;AACtC,YAAI,gBAAgB,iBAAiB,QAAS,mBAAkB;YAC3D,YAAW;MAClB,GAAG,CAAC,gBAAgB,kBAAkB,mBAAmB,UAAU,CAAC;MACpE,gBAAsB,kBAAY,MAAM;AACtC,YAAI,yBAAyB;AAC3B,sBAAY;QACd,OAAO;AAEL,iBAAO,aAAa,aAAa,OAAO;AACxC,uBAAa,UAAU;QACzB;MACF,GAAG,CAAC,aAAa,uBAAuB,CAAC;MACzC,QAAQ;MACR,SAAS;MACT;MAEC;IAAA;EACH,EAAA,CACF;AAEJ;AAEA,QAAQ,cAAc;AAMtB,IAAM,eAAe;AAMrB,IAAM,iBAAuB;EAC3B,CAAC,OAAyC,iBAAiB;AACzD,UAAM,EAAE,gBAAgB,GAAG,aAAa,IAAI;AAC5C,UAAM,UAAU,kBAAkB,cAAc,cAAc;AAC9D,UAAM,kBAAkB,0BAA0B,cAAc,cAAc;AAC9E,UAAM,cAAc,eAAe,cAAc;AACjD,UAAM,MAAY,aAA8B,IAAI;AACpD,UAAM,eAAe,gBAAgB,cAAc,KAAK,QAAQ,eAAe;AAC/E,UAAM,mBAAyB,aAAO,KAAK;AAC3C,UAAM,0BAAgC,aAAO,KAAK;AAClD,UAAM,kBAAwB,kBAAY,MAAO,iBAAiB,UAAU,OAAQ,CAAC,CAAC;AAEhF,IAAA,gBAAU,MAAM;AACpB,aAAO,MAAM,SAAS,oBAAoB,aAAa,eAAe;IACxE,GAAG,CAAC,eAAe,CAAC;AAEpB,eACE,wBAAiB,QAAhB,EAAuB,SAAO,MAAE,GAAG,aAClC,cAAA;MAAC,UAAU;MAAV;QAGC,oBAAkB,QAAQ,OAAO,QAAQ,YAAY;QACrD,cAAY,QAAQ;QACnB,GAAG;QACJ,KAAK;QACL,eAAe,qBAAqB,MAAM,eAAe,CAAC,UAAU;AAClE,cAAI,MAAM,gBAAgB,QAAS;AACnC,cACE,CAAC,wBAAwB,WACzB,CAAC,gBAAgB,sBAAsB,SACvC;AACA,oBAAQ,eAAe;AACvB,oCAAwB,UAAU;UACpC;QACF,CAAC;QACD,gBAAgB,qBAAqB,MAAM,gBAAgB,MAAM;AAC/D,kBAAQ,eAAe;AACvB,kCAAwB,UAAU;QACpC,CAAC;QACD,eAAe,qBAAqB,MAAM,eAAe,MAAM;AAC7D,cAAI,QAAQ,MAAM;AAChB,oBAAQ,QAAQ;UAClB;AACA,2BAAiB,UAAU;AAC3B,mBAAS,iBAAiB,aAAa,iBAAiB,EAAE,MAAM,KAAK,CAAC;QACxE,CAAC;QACD,SAAS,qBAAqB,MAAM,SAAS,MAAM;AACjD,cAAI,CAAC,iBAAiB,QAAS,SAAQ,OAAO;QAChD,CAAC;QACD,QAAQ,qBAAqB,MAAM,QAAQ,QAAQ,OAAO;QAC1D,SAAS,qBAAqB,MAAM,SAAS,QAAQ,OAAO;MAAA;IAC9D,EAAA,CACF;EAEJ;AACF;AAEA,eAAe,cAAc;AAM7B,IAAM,cAAc;AAGpB,IAAM,CAAC,gBAAgB,gBAAgB,IAAI,qBAAyC,aAAa;EAC/F,YAAY;AACd,CAAC;AAgBD,IAAM,gBAA8C,CAAC,UAA2C;AAC9F,QAAM,EAAE,gBAAgB,YAAY,UAAU,UAAU,IAAI;AAC5D,QAAM,UAAU,kBAAkB,aAAa,cAAc;AAC7D,aACE,wBAAC,gBAAA,EAAe,OAAO,gBAAgB,YACrC,cAAA,wBAAC,UAAA,EAAS,SAAS,cAAc,QAAQ,MACvC,cAAA,wBAAC,QAAA,EAAgB,SAAO,MAAC,WACtB,SAAA,CACH,EAAA,CACF,EAAA,CACF;AAEJ;AAEA,cAAc,cAAc;AAM5B,IAAM,eAAe;AAWrB,IAAM,iBAAuB;EAC3B,CAAC,OAAyC,iBAAiB;AACzD,UAAM,gBAAgB,iBAAiB,cAAc,MAAM,cAAc;AACzE,UAAM,EAAE,aAAa,cAAc,YAAY,OAAO,OAAO,GAAG,aAAa,IAAI;AACjF,UAAM,UAAU,kBAAkB,cAAc,MAAM,cAAc;AAEpE,eACE,wBAAC,UAAA,EAAS,SAAS,cAAc,QAAQ,MACtC,UAAA,QAAQ,8BACP,wBAAC,oBAAA,EAAmB,MAAa,GAAG,cAAc,KAAK,aAAA,CAAc,QAErE,wBAAC,yBAAA,EAAwB,MAAa,GAAG,cAAc,KAAK,aAAA,CAAc,EAAA,CAE9E;EAEJ;AACF;AAQA,IAAM,0BAAgC,iBAGpC,CAAC,OAAkD,iBAAiB;AACpE,QAAM,UAAU,kBAAkB,cAAc,MAAM,cAAc;AACpE,QAAM,kBAAkB,0BAA0B,cAAc,MAAM,cAAc;AACpF,QAAM,MAAY,aAAuC,IAAI;AAC7D,QAAM,eAAe,gBAAgB,cAAc,GAAG;AACtD,QAAM,CAAC,kBAAkB,mBAAmB,IAAU,eAAyB,IAAI;AAEnF,QAAM,EAAE,SAAS,QAAQ,IAAI;AAC7B,QAAM,UAAU,IAAI;AAEpB,QAAM,EAAE,yBAAyB,IAAI;AAErC,QAAM,wBAA8B,kBAAY,MAAM;AACpD,wBAAoB,IAAI;AACxB,6BAAyB,KAAK;EAChC,GAAG,CAAC,wBAAwB,CAAC;AAE7B,QAAM,wBAA8B;IAClC,CAAC,OAAqB,gBAA6B;AACjD,YAAM,gBAAgB,MAAM;AAC5B,YAAM,YAAY,EAAE,GAAG,MAAM,SAAS,GAAG,MAAM,QAAQ;AACvD,YAAM,WAAW,oBAAoB,WAAW,cAAc,sBAAsB,CAAC;AACrF,YAAM,mBAAmB,oBAAoB,WAAW,QAAQ;AAChE,YAAM,oBAAoB,kBAAkB,YAAY,sBAAsB,CAAC;AAC/E,YAAM,YAAY,QAAQ,CAAC,GAAG,kBAAkB,GAAG,iBAAiB,CAAC;AACrE,0BAAoB,SAAS;AAC7B,+BAAyB,IAAI;IAC/B;IACA,CAAC,wBAAwB;EAC3B;AAEM,EAAA,gBAAU,MAAM;AACpB,WAAO,MAAM,sBAAsB;EACrC,GAAG,CAAC,qBAAqB,CAAC;AAEpB,EAAA,gBAAU,MAAM;AACpB,QAAI,WAAW,SAAS;AACtB,YAAM,qBAAqB,CAAC,UAAwB,sBAAsB,OAAO,OAAO;AACxF,YAAM,qBAAqB,CAAC,UAAwB,sBAAsB,OAAO,OAAO;AAExF,cAAQ,iBAAiB,gBAAgB,kBAAkB;AAC3D,cAAQ,iBAAiB,gBAAgB,kBAAkB;AAC3D,aAAO,MAAM;AACX,gBAAQ,oBAAoB,gBAAgB,kBAAkB;AAC9D,gBAAQ,oBAAoB,gBAAgB,kBAAkB;MAChE;IACF;EACF,GAAG,CAAC,SAAS,SAAS,uBAAuB,qBAAqB,CAAC;AAE7D,EAAA,gBAAU,MAAM;AACpB,QAAI,kBAAkB;AACpB,YAAM,0BAA0B,CAAC,UAAwB;AACvD,cAAM,SAAS,MAAM;AACrB,cAAM,kBAAkB,EAAE,GAAG,MAAM,SAAS,GAAG,MAAM,QAAQ;AAC7D,cAAM,oBAAmB,mCAAS,SAAS,aAAW,mCAAS,SAAS;AACxE,cAAM,4BAA4B,CAAC,iBAAiB,iBAAiB,gBAAgB;AAErF,YAAI,kBAAkB;AACpB,gCAAsB;QACxB,WAAW,2BAA2B;AACpC,gCAAsB;AACtB,kBAAQ;QACV;MACF;AACA,eAAS,iBAAiB,eAAe,uBAAuB;AAChE,aAAO,MAAM,SAAS,oBAAoB,eAAe,uBAAuB;IAClF;EACF,GAAG,CAAC,SAAS,SAAS,kBAAkB,SAAS,qBAAqB,CAAC;AAEvE,aAAO,wBAAC,oBAAA,EAAoB,GAAG,OAAO,KAAK,aAAA,CAAc;AAC3D,CAAC;AAED,IAAM,CAAC,sCAAsC,+BAA+B,IAC1E,qBAAqB,cAAc,EAAE,UAAU,MAAM,CAAC;AAuBxD,IAAM,YAAY,gBAAgB,gBAAgB;AAElD,IAAM,qBAA2B;EAC/B,CAAC,OAA6C,iBAAiB;AAC7D,UAAM;MACJ;MACA;MACA,cAAc;MACd;MACA;MACA,GAAG;IACL,IAAI;AACJ,UAAM,UAAU,kBAAkB,cAAc,cAAc;AAC9D,UAAM,cAAc,eAAe,cAAc;AACjD,UAAM,EAAE,QAAQ,IAAI;AAGd,IAAA,gBAAU,MAAM;AACpB,eAAS,iBAAiB,cAAc,OAAO;AAC/C,aAAO,MAAM,SAAS,oBAAoB,cAAc,OAAO;IACjE,GAAG,CAAC,OAAO,CAAC;AAGN,IAAA,gBAAU,MAAM;AACpB,UAAI,QAAQ,SAAS;AACnB,cAAM,eAAe,CAAC,UAAiB;AACrC,gBAAM,SAAS,MAAM;AACrB,cAAI,iCAAQ,SAAS,QAAQ,SAAU,SAAQ;QACjD;AACA,eAAO,iBAAiB,UAAU,cAAc,EAAE,SAAS,KAAK,CAAC;AACjE,eAAO,MAAM,OAAO,oBAAoB,UAAU,cAAc,EAAE,SAAS,KAAK,CAAC;MACnF;IACF,GAAG,CAAC,QAAQ,SAAS,OAAO,CAAC;AAE7B,eACE;MAAC;MAAA;QACC,SAAO;QACP,6BAA6B;QAC7B;QACA;QACA,gBAAgB,CAAC,UAAU,MAAM,eAAe;QAChD,WAAW;QAEX,cAAA;UAAiB;UAAhB;YACC,cAAY,QAAQ;YACnB,GAAG;YACH,GAAG;YACJ,KAAK;YACL,OAAO;cACL,GAAG,aAAa;;cAEhB,GAAG;gBACD,4CAA4C;gBAC5C,2CAA2C;gBAC3C,4CAA4C;gBAC5C,iCAAiC;gBACjC,kCAAkC;cACpC;YACF;YAEA,UAAA;kBAAA,wBAAC,WAAA,EAAW,SAAA,CAAS;kBACrB,wBAAC,sCAAA,EAAqC,OAAO,gBAAgB,UAAU,MACrE,cAAA,wBAAyB,MAAxB,EAA6B,IAAI,QAAQ,WAAW,MAAK,WACvD,UAAA,aAAa,SAAA,CAChB,EAAA,CACF;YAAA;UAAA;QACF;MAAA;IACF;EAEJ;AACF;AAEA,eAAe,cAAc;AAM7B,IAAM,aAAa;AAMnB,IAAM,eAAqB;EACzB,CAAC,OAAuC,iBAAiB;AACvD,UAAM,EAAE,gBAAgB,GAAG,WAAW,IAAI;AAC1C,UAAM,cAAc,eAAe,cAAc;AACjD,UAAM,+BAA+B;MACnC;MACA;IACF;AAGA,WAAO,6BAA6B,WAAW,WAC7C,wBAAiB,OAAhB,EAAuB,GAAG,aAAc,GAAG,YAAY,KAAK,aAAA,CAAc;EAE/E;AACF;AAEA,aAAa,cAAc;AAM3B,SAAS,oBAAoB,OAAc,MAAqB;AAC9D,QAAM,MAAM,KAAK,IAAI,KAAK,MAAM,MAAM,CAAC;AACvC,QAAM,SAAS,KAAK,IAAI,KAAK,SAAS,MAAM,CAAC;AAC7C,QAAM,QAAQ,KAAK,IAAI,KAAK,QAAQ,MAAM,CAAC;AAC3C,QAAM,OAAO,KAAK,IAAI,KAAK,OAAO,MAAM,CAAC;AAEzC,UAAQ,KAAK,IAAI,KAAK,QAAQ,OAAO,IAAI,GAAG;IAC1C,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT;AACE,YAAM,IAAI,MAAM,aAAa;EACjC;AACF;AAEA,SAAS,oBAAoB,WAAkB,UAAgB,UAAU,GAAG;AAC1E,QAAM,mBAA4B,CAAC;AACnC,UAAQ,UAAU;IAChB,KAAK;AACH,uBAAiB;QACf,EAAE,GAAG,UAAU,IAAI,SAAS,GAAG,UAAU,IAAI,QAAQ;QACrD,EAAE,GAAG,UAAU,IAAI,SAAS,GAAG,UAAU,IAAI,QAAQ;MACvD;AACA;IACF,KAAK;AACH,uBAAiB;QACf,EAAE,GAAG,UAAU,IAAI,SAAS,GAAG,UAAU,IAAI,QAAQ;QACrD,EAAE,GAAG,UAAU,IAAI,SAAS,GAAG,UAAU,IAAI,QAAQ;MACvD;AACA;IACF,KAAK;AACH,uBAAiB;QACf,EAAE,GAAG,UAAU,IAAI,SAAS,GAAG,UAAU,IAAI,QAAQ;QACrD,EAAE,GAAG,UAAU,IAAI,SAAS,GAAG,UAAU,IAAI,QAAQ;MACvD;AACA;IACF,KAAK;AACH,uBAAiB;QACf,EAAE,GAAG,UAAU,IAAI,SAAS,GAAG,UAAU,IAAI,QAAQ;QACrD,EAAE,GAAG,UAAU,IAAI,SAAS,GAAG,UAAU,IAAI,QAAQ;MACvD;AACA;EACJ;AACA,SAAO;AACT;AAEA,SAAS,kBAAkB,MAAe;AACxC,QAAM,EAAE,KAAK,OAAO,QAAQ,KAAK,IAAI;AACrC,SAAO;IACL,EAAE,GAAG,MAAM,GAAG,IAAI;IAClB,EAAE,GAAG,OAAO,GAAG,IAAI;IACnB,EAAE,GAAG,OAAO,GAAG,OAAO;IACtB,EAAE,GAAG,MAAM,GAAG,OAAO;EACvB;AACF;AAIA,SAAS,iBAAiB,OAAc,SAAkB;AACxD,QAAM,EAAE,GAAG,EAAE,IAAI;AACjB,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,QAAQ,SAAS,GAAG,IAAI,QAAQ,QAAQ,IAAI,KAAK;AACnE,UAAM,KAAK,QAAQ,CAAC;AACpB,UAAM,KAAK,QAAQ,CAAC;AACpB,UAAM,KAAK,GAAG;AACd,UAAM,KAAK,GAAG;AACd,UAAM,KAAK,GAAG;AACd,UAAM,KAAK,GAAG;AAGd,UAAM,YAAc,KAAK,MAAQ,KAAK,KAAQ,KAAK,KAAK,OAAO,IAAI,OAAO,KAAK,MAAM;AACrF,QAAI,UAAW,UAAS,CAAC;EAC3B;AAEA,SAAO;AACT;AAIA,SAAS,QAAyB,QAAsC;AACtE,QAAM,YAAsB,OAAO,MAAM;AACzC,YAAU,KAAK,CAAC,GAAU,MAAa;AACrC,QAAI,EAAE,IAAI,EAAE,EAAG,QAAO;aACb,EAAE,IAAI,EAAE,EAAG,QAAO;aAClB,EAAE,IAAI,EAAE,EAAG,QAAO;aAClB,EAAE,IAAI,EAAE,EAAG,QAAO;QACtB,QAAO;EACd,CAAC;AACD,SAAO,iBAAiB,SAAS;AACnC;AAGA,SAAS,iBAAkC,QAAsC;AAC/E,MAAI,OAAO,UAAU,EAAG,QAAO,OAAO,MAAM;AAE5C,QAAM,YAAsB,CAAC;AAC7B,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAM,IAAI,OAAO,CAAC;AAClB,WAAO,UAAU,UAAU,GAAG;AAC5B,YAAM,IAAI,UAAU,UAAU,SAAS,CAAC;AACxC,YAAM,IAAI,UAAU,UAAU,SAAS,CAAC;AACxC,WAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,GAAI,WAAU,IAAI;UACrE;IACP;AACA,cAAU,KAAK,CAAC;EAClB;AACA,YAAU,IAAI;AAEd,QAAM,YAAsB,CAAC;AAC7B,WAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,UAAM,IAAI,OAAO,CAAC;AAClB,WAAO,UAAU,UAAU,GAAG;AAC5B,YAAM,IAAI,UAAU,UAAU,SAAS,CAAC;AACxC,YAAM,IAAI,UAAU,UAAU,SAAS,CAAC;AACxC,WAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,GAAI,WAAU,IAAI;UACrE;IACP;AACA,cAAU,KAAK,CAAC;EAClB;AACA,YAAU,IAAI;AAEd,MACE,UAAU,WAAW,KACrB,UAAU,WAAW,KACrB,UAAU,CAAC,EAAG,MAAM,UAAU,CAAC,EAAG,KAClC,UAAU,CAAC,EAAG,MAAM,UAAU,CAAC,EAAG,GAClC;AACA,WAAO;EACT,OAAO;AACL,WAAO,UAAU,OAAO,SAAS;EACnC;AACF;AAEA,IAAM,WAAW;AACjB,IAAMC,QAAO;AACb,IAAM,UAAU;AAChB,IAAMC,UAAS;AACf,IAAMC,WAAU;AAChB,IAAMC,SAAQ;", "names": ["open", "Root", "Portal", "Content", "Arrow"]}