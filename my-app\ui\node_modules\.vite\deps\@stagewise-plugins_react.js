"use client";
import "./chunk-DC5AMYBS.js";

// ../node_modules/.pnpm/@stagewise-plugins+react@0.6.2_@stagewise+toolbar@0.6.2/node_modules/@stagewise-plugins/react/dist/index.es.js
var plugin = {
  mainPlugin: '"use client";\nimport { jsxs as p, jsx as a } from "react/jsx-runtime";\nfunction f() {\n  return /* @__PURE__ */ p(\n    "svg",\n    {\n      xmlns: "http://www.w3.org/2000/svg",\n      viewBox: "-11.5 -10.23174 23 20.46348",\n      children: [\n        /* @__PURE__ */ a("title", { children: "React Logo" }),\n        /* @__PURE__ */ a("circle", { cx: "0", cy: "0", r: "2.05", fill: "currentColor" }),\n        /* @__PURE__ */ p("g", { stroke: "currentColor", strokeWidth: "1", fill: "none", children: [\n          /* @__PURE__ */ a("ellipse", { rx: "11", ry: "4.2" }),\n          /* @__PURE__ */ a("ellipse", { rx: "11", ry: "4.2", transform: "rotate(60)" }),\n          /* @__PURE__ */ a("ellipse", { rx: "11", ry: "4.2", transform: "rotate(120)" })\n        ] })\n      ]\n    }\n  );\n}\nconst g = 0, h = 1, y = 5;\nfunction x(n) {\n  return d(n.ownProperties);\n}\nfunction d(n) {\n  var i, m;\n  if (!n)\n    return null;\n  const e = [], r = 3, s = Object.keys(n).find(\n    (o) => o.startsWith("__reactFiber$") || o.startsWith("__reactInternalInstance$")\n  );\n  if (!s)\n    return null;\n  let t = n[s];\n  if (!t)\n    return null;\n  for (; t && e.length < r; ) {\n    let o = null;\n    if (t.tag === h || t.tag === g) {\n      const l = t.type;\n      l && (o = { name: l.displayName || l.name || ((i = t._debugOwner) == null ? void 0 : i.name) || // Check direct name on fiber\n      "AnonymousComponent", type: "regular" });\n    } else t.tag === y && t._debugOwner && ((m = t._debugOwner.env) != null && m.toLowerCase().includes("server")) && (o = { name: t._debugOwner.name, type: "rsc" });\n    o && (e.some(\n      (c) => c.name === o.name && c.type === o.type\n    ) || e.push(o)), t = t.return;\n  }\n  return e.length > 0 ? e : null;\n}\nfunction u(n) {\n  const e = d(n);\n  return e != null && e[0] ? {\n    annotation: `${e[0].name}${e[0].type === "rsc" ? " (RSC)" : ""}`\n  } : { annotation: null };\n}\nfunction C(n) {\n  const e = n.map(\n    (r) => x(r)\n  );\n  return e.some((r) => r && r.length > 0) ? `This is additional information on the elements that the user selected. Use this information to find the correct element in the codebase.\n\n  ${e.map((s, t) => `\n<element index="${t + 1}">\n  ${s.length === 0 ? "No React component as parent detected" : `React component tree (from closest to farthest, 3 closest elements): ${s.map((i) => `{name: ${i.name}, type: ${i.type}}`).join(" child of ")}`}\n</element>\n    `)}\n  ` : null;\n}\nconst R = {\n  displayName: "React",\n  description: "This plugin adds additional information and metadata for apps using React as a UI framework",\n  iconSvg: /* @__PURE__ */ a(f, {}),\n  pluginName: "react",\n  onContextElementHover: u,\n  onContextElementSelect: u,\n  onPromptSend: (n) => {\n    const e = C(n.metadata.selectedElements);\n    return e ? {\n      contextSnippets: [\n        {\n          promptContextName: "elements-react-component-info",\n          content: e\n        }\n      ]\n    } : { contextSnippets: [] };\n  }\n};\nexport {\n  R as default\n};\n',
  loader: true
};
var index_es_default = plugin;
export {
  index_es_default as default
};
//# sourceMappingURL=@stagewise-plugins_react.js.map
